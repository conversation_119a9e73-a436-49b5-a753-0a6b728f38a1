{"name": "ui", "version": "1.0.0", "private": true, "keywords": [], "license": "ISC", "author": "", "type": "module", "main": "index.js", "files": [".vinxi", ".output", ".vercel"], "scripts": {"build": "tsc && vinxi build", "dev": "vinxi dev --port 3002", "generate": "openapi-typescript ../api/openapi.json -o ./src/lib/api/v1.d.ts", "start": "vinxi start"}, "dependencies": {"@hey-api/client-fetch": "0.10.1", "@hookform/resolvers": "5.0.1", "@llmgateway/models": "workspace:*", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-scroll-area": "1.2.9", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "1.2.14", "@radix-ui/react-tooltip": "^1.2.6", "@stripe/react-stripe-js": "3.7.0", "@stripe/stripe-js": "7.4.0", "@tanstack/react-query": "^5.74.7", "@tanstack/react-router": "^1.117.1", "@tanstack/react-router-with-query": "^1.123.2", "@tanstack/react-start": "^1.118.0", "@tanstack/react-table": "^8.21.2", "@tanstack/router-core": "1.120.3", "@tanstack/router-plugin": "^1.119.0", "api": "workspace:*", "better-auth": "1.2.7", "class-variance-authority": "0.7.1", "clsx": "^2.0.0", "crisp-sdk-web": "1.0.25", "date-fns": "4.1.0", "framer-motion": "12.15.0", "lucide-react": "^0.488.0", "markdown-to-jsx": "7.7.7", "motion": "12.15.0", "next-themes": "^0.4.6", "openapi-fetch": "0.14.0", "openapi-react-query": "0.5.0", "posthog-js": "1.255.1", "pretty-bytes": "7.0.0", "prism-react-renderer": "2.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "7.56.1", "recharts": "^2.15.2", "rollup-plugin-copy": "3.5.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "4.0.15", "tailwindcss-animate": "1.0.7", "vinxi": "^0.5.3", "vite-plugin-svgr": "4.3.0"}, "devDependencies": {"@content-collections/core": "0.9.1", "@content-collections/vinxi": "0.1.1", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-router-devtools": "^1.120.3", "@types/node": "24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "10.4.15", "openapi-typescript": "7.8.0", "postcss": "8.5.4", "typescript": "^5.8.2", "vite": "^6.3.4", "vite-tsconfig-paths": "^5.1.4", "zod": "3.24.3"}}